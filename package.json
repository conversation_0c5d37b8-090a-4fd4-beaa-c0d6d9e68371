{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run websocket\" \"npm run api\" \"npm run vite\" --names \"WS,API,VITE\" --prefix-colors \"blue,yellow,green\"", "dev:vite-only": "vite", "vite": "vite", "websocket": "node websocket-server.js", "api": "node api-server.js", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@blocknote/core": "^0.32.0", "@blocknote/mantine": "^0.32.0", "@blocknote/react": "^0.32.0", "@codemirror/basic-setup": "^0.20.0", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-markdown": "^6.3.3", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.0", "@liveblocks/client": "^3.0.0", "@liveblocks/react": "^3.0.0", "@liveblocks/yjs": "^3.0.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^9.120.1", "@react-three/fiber": "^8.17.10", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.56.2", "@tiptap/extension-collaboration": "^2.22.3", "@tiptap/extension-collaboration-cursor": "^2.22.3", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@types/lodash.debounce": "^4.0.9", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "codemirror": "^6.0.2", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "framer-motion": "^12.18.1", "input-otp": "^1.2.4", "lodash.debounce": "^4.0.8", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "quill": "^2.0.3", "quill-cursors": "^4.0.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "remark-gfm": "^4.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "vaul": "^0.9.3", "ws": "^8.18.2", "y-codemirror.next": "^0.3.5", "y-quill": "^1.0.0", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zod": "^3.25.32"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}