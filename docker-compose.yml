# Optional Docker setup for the collaborative editor
# Run with: docker-compose up

version: '3.8'

services:
  websocket-server:
    build:
      context: .
      dockerfile: Dockerfile.websocket
    ports:
      - "1234:1234"
    environment:
      - NODE_ENV=development
    volumes:
      - ./websocket-server.js:/app/websocket-server.js
    restart: unless-stopped

  vite-dev:
    build:
      context: .
      dockerfile: Dockerfile.vite
    ports:
      - "8081:8081"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - websocket-server
    restart: unless-stopped
