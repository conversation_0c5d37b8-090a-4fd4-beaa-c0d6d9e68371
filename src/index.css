@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations and transitions */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@layer base {
  :root {
    /* Light theme with greyish/off-white backgrounds for better visibility and contrast */
    --background: 210 20% 95%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 97%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 97%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 92%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 90%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 92%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 85%;
    --input: 214.3 31.8% 85%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 210 20% 93%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 89%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 84%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced light theme styling for better visibility */
@layer components {
  .light-theme-enhanced {
    background: hsl(210 20% 94%);
  }

  .light .bg-background {
    background: hsl(210 20% 95%);
  }

  .light .bg-card {
    background: hsl(0 0% 97%);
  }

  .light .bg-muted {
    background: hsl(210 40% 90%);
  }

  /* Enhanced contrast for light theme borders */
  .light .border {
    border-color: hsl(214.3 31.8% 82%);
  }

  /* Better shadow for light theme */
  .light .shadow-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.08);
  }

  .light .shadow-md {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.12), 0 2px 4px -2px rgb(0 0 0 / 0.08);
  }

  .light .shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.12), 0 4px 6px -4px rgb(0 0 0 / 0.08);
  }
}

/* Global Scrollbar Styling - Theme Aware */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted) / 0.1);
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.1);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

*::-webkit-scrollbar-thumb:active {
  background: hsl(var(--muted-foreground) / 0.7);
}

*::-webkit-scrollbar-corner {
  background: hsl(var(--muted) / 0.1);
}

/* TipTap Editor Styles */
.ProseMirror {
  min-height: 100%;
  outline: none;
}

.ProseMirror p {
  margin: 1em 0;
  color: hsl(var(--foreground));
}

.ProseMirror h1 {
  font-size: 2em;
  margin: 0.67em 0;
  color: hsl(var(--foreground));
  font-weight: 600;
}

.ProseMirror h2 {
  font-size: 1.5em;
  margin: 0.83em 0;
  color: hsl(var(--foreground));
  font-weight: 600;
}

.ProseMirror ul {
  list-style-type: disc;
  padding: 0 1em;
  margin: 1em 0;
  color: hsl(var(--foreground));
}

.ProseMirror ol {
  list-style-type: decimal;
  padding: 0 1em;
  margin: 1em 0;
  color: hsl(var(--foreground));
}

.ProseMirror li {
  margin: 0.5em 0;
  color: hsl(var(--foreground));
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 1em 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
  border: 1px solid hsl(var(--border));
}

.ProseMirror td,
.ProseMirror th {
  border: 1px solid hsl(var(--border));
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
  color: hsl(var(--foreground));
}

.ProseMirror th {
  background-color: hsl(var(--muted));
  font-weight: bold;
  text-align: left;
}

.ProseMirror .selectedCell:after {
  background: hsl(var(--accent));
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
  opacity: 0.2;
}

.ProseMirror .column-resize-handle {
  background-color: hsl(var(--primary));
  bottom: -2px;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
  opacity: 0.5;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: hsl(var(--muted-foreground));
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Remove focus outline */
.ProseMirror:focus {
  outline: none;
}

/* ProseMirror scrollbar - inherits from global styles */
.ProseMirror {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted) / 0.1);
}

.ProseMirror::-webkit-scrollbar {
  width: 8px;
}

.ProseMirror::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.1);
  border-radius: 4px;
}

.ProseMirror::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.ProseMirror::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Hide main page scrollbar while keeping scroll functionality */
html {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

html::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

body {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

body::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

/* Grid pattern background for enhanced UI sections */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, hsl(var(--muted-foreground) / 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--muted-foreground) / 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced similarity search animations */
@keyframes float-in {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-float-in {
  animation: float-in 0.4s ease-out forwards;
}

/* Gradient text animations */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

/* Auto-scrolling carousel animation */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.auto-scroll {
  animation: scroll-left 20s linear infinite;
}

.auto-scroll:hover {
  animation-play-state: paused;
}