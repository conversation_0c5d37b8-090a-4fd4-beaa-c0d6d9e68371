/* BlockNote Custom Theming for IdeaVault */

/* Main editor container */
.bn-container {
  background: #1C1C1C !important;
  color: #ffffff !important;
  border: 1.5px solid #5A827E !important;
  border-radius: 1rem !important;
  height: 100% !important;
  max-height: none !important;
  overflow: visible !important;
}

.bn-editor {
  background: #1C1C1C !important;
  color: #ffffff !important;
  font-family: 'Inter', system-ui, sans-serif !important;
  border: 1.5px solid #5A827E !important;
  border-radius: 1rem !important;
  height: 100% !important;
  max-height: none !important;
  overflow: visible !important;
}

/* Editor content area */
.ProseMirror {
  background: #1C1C1C !important;
  color: #ffffff !important;
  padding: 3rem 4rem !important;
  min-height: 100% !important;
  height: 100% !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
  max-height: none !important;
  overflow: visible !important;
  box-sizing: border-box !important;
}

.ProseMirror:focus {
  outline: none !important;
}

/* Block containers */
.bn-block-outer {
  background: transparent !important;
}

.bn-block-content {
  background: transparent !important;
  color: #ffffff !important;
}

.bn-inline-content {
  color: #ffffff !important;
}

/* Block handles and buttons */
.bn-block-side-menu {
  background: linear-gradient(135deg, #1C1C1C 80%, #5A827E 100%) !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 24px 0 #5A827E22 !important;
}

.bn-block-side-menu button {
  color: #ffffff !important;
  background: transparent !important;
}

.bn-block-side-menu button:hover {
  background: #5A827E20 !important;
  color: #ffffff !important;
}

/* Formatting toolbar */
.bn-formatting-toolbar {
  background: linear-gradient(135deg, #1C1C1C 80%, #5A827E 100%) !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 24px 0 #5A827E22 !important;
}

.bn-formatting-toolbar button {
  color: #ffffff !important;
  background: transparent !important;
}

.bn-formatting-toolbar button:hover {
  background: #5A827E20 !important;
  color: #ffffff !important;
}

.bn-formatting-toolbar button[data-state="on"] {
  background: #5A827E !important;
  color: #ffffff !important;
}

/* Slash menu */
.bn-slash-menu {
  background: linear-gradient(135deg, #1C1C1C 80%, #5A827E 100%) !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 24px 0 #5A827E22 !important;
}

.bn-slash-menu-item {
  color: #ffffff !important;
  background: transparent !important;
}

.bn-slash-menu-item:hover,
.bn-slash-menu-item[data-highlighted] {
  background: #5A827E20 !important;
  color: #ffffff !important;
}

.bn-slash-menu-item-selected {
  background: #5A827E !important;
  color: #ffffff !important;
}

/* Link toolbar */
.bn-link-toolbar {
  background: linear-gradient(135deg, #1C1C1C 80%, #5A827E 100%) !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 24px 0 #5A827E22 !important;
}

.bn-link-toolbar input {
  background: #232e2b !important;
  color: #ffffff !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.375rem !important;
}

.bn-link-toolbar button {
  color: #ffffff !important;
  background: transparent !important;
}

.bn-link-toolbar button:hover {
  background: #5A827E20 !important;
}

/* Table menus */
.bn-table-menu {
  background: linear-gradient(135deg, #1C1C1C 80%, #5A827E 100%) !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 24px 0 #5A827E22 !important;
}

.bn-table-menu button {
  color: #ffffff !important;
  background: transparent !important;
}

.bn-table-menu button:hover {
  background: #5A827E20 !important;
}

/* Placeholders */
.ProseMirror p.is-empty::before {
  color: #B9D4AA !important;
  opacity: 0.7;
  float: left;
  height: 0;
  pointer-events: none;
}

/* Headings */
.ProseMirror h1 {
  color: #ffffff !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 1.5rem 0 1rem 0 !important;
}

.ProseMirror h2 {
  color: #ffffff !important;
  font-size: 2rem !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin: 1.25rem 0 0.75rem 0 !important;
}

.ProseMirror h3 {
  color: #ffffff !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 1rem 0 0.5rem 0 !important;
}

/* Lists */
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

.ProseMirror li {
  margin: 0.25rem 0 !important;
  color: #ffffff !important;
}

/* Blockquotes */
.ProseMirror blockquote {
  border-left: 4px solid #5A827E !important;
  padding-left: 1rem !important;
  margin: 1rem 0 !important;
  font-style: italic !important;
  color: #B9D4AA !important;
  background: #5A827E22 !important;
}

/* Code blocks */
.ProseMirror pre {
  background: #232e2b !important;
  color: #ffffff !important;
  border-radius: 0.5rem !important;
  padding: 1rem !important;
  margin: 1rem 0 !important;
  overflow-x: auto !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  border: 1px solid #5A827E40 !important;
}

.ProseMirror code {
  background: #232e2b !important;
  color: #B9D4AA !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.875em !important;
  border: 1px solid #5A827E40 !important;
}

/* Tables */
.ProseMirror table {
  border-collapse: collapse !important;
  margin: 1rem 0 !important;
  width: 100% !important;
}

.ProseMirror td,
.ProseMirror th {
  border: 1px solid #5A827E !important;
  padding: 0.5rem !important;
  color: #ffffff !important;
  background: transparent !important;
}

.ProseMirror th {
  background: #5A827E20 !important;
  font-weight: 600 !important;
  color: #ffffff !important;
}

/* Links */
.ProseMirror a {
  color: #84AE92 !important;
  text-decoration: underline !important;
  text-decoration-color: #B9D4AA !important;
}

.ProseMirror a:hover {
  color: #B9D4AA !important;
}

/* Selection */
.ProseMirror ::selection {
  background: #84AE92cc !important;
  color: #ffffff !important;
}

/* Collaboration cursors */
.collaboration-cursor__caret {
  border-left: 2px solid !important;
  margin-left: -1px !important;
  margin-right: -1px !important;
  pointer-events: none !important;
  position: relative !important;
  word-break: normal !important;
}

.collaboration-cursor__label {
  background: inherit !important;
  border-radius: 0.25rem !important;
  color: white !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  left: -1px !important;
  line-height: 1 !important;
  padding: 0.25rem 0.5rem !important;
  position: absolute !important;
  top: -1.75rem !important;
  user-select: none !important;
  white-space: nowrap !important;
}

/* ----------------- 🛠 PERFECT Alignment Fix ----------------- */

.ProseMirror {
  position: relative !important;
  padding-left: 4rem !important; /* Space for side menu */
}

/* Fix side menu positioning to align perfectly with text line */
.bn-block-side-menu {
  position: absolute !important;
  top: 0 !important;
  left: 0.5rem !important;
  transform: translateY(0) !important; /* Reset transform */
  z-index: 10 !important;
  background: #1C1C1C !important;
  border: 1px solid #5A827E !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  display: flex !important;
  align-items: center !important;
  height: 1.5rem !important; /* Match line height exactly */
  margin-top: 0 !important;
}

/* Ensure buttons in side menu are properly sized and aligned */
.bn-block-side-menu button {
  width: 1.5rem !important;
  height: 1.5rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

/* Align block content properly */
.bn-block-content {
  padding-left: 0.25rem !important;
  line-height: 1.5rem !important; /* Exact line height */
  min-height: 1.5rem !important; /* Ensure minimum height matches */
}

/* Fix positioning for all block types */
.bn-block-outer {
  position: relative !important;
}

.bn-block-outer .bn-block-side-menu {
  top: 0 !important;
  margin-top: 0 !important;
  transform: translateY(0) !important;
}

/* Ensure paragraph alignment */
.ProseMirror p {
  line-height: 1.5rem !important;
  margin: 0 !important;
  min-height: 1.5rem !important;
}

/* Slash menu positioning fix */
.bn-slash-menu {
  margin-left: -2rem !important;
  z-index: 50 !important;
}

/* Fix cursor positioning */
.collaboration-cursor__caret {
  z-index: 20 !important;
  margin-left: 0 !important;
}